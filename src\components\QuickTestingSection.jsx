import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Home8 from "../Asserts/Home8.jpg";

gsap.registerPlugin(ScrollTrigger);

// Enhanced SVG Blob for a more modern feel
const ModernBlob1 = () => (
  <svg width="500" height="500" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.08, position: 'absolute', top: '-100px', right: '-150px' }}>
    <defs>
      <linearGradient id="testingBlobGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#007bff', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#testingBlobGradient1)" d="M60.1,-66.9C76.5,-56.8,87.5,-37.9,89.9,-18.3C92.3,1.3,86.1,21.5,74.1,37.9C62.1,54.3,44.3,67,25.5,73.7C6.7,80.4,-13.2,81.2,-30.9,74.8C-48.6,68.4,-64.1,54.8,-72.4,38.2C-80.7,21.6,-81.8,2,-76.5,-16.1C-71.2,-34.2,-59.5,-50.8,-44.4,-61C-29.3,-71.1,-10.8,-74.7,9.3,-77.2C29.4,-79.7,51.1,-82.3,60.1,-66.9Z" transform="translate(100 100) scale(1.1)" />
  </svg>
);

const ModernBlob2 = () => (
  <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style={{ zIndex: 0, opacity: 0.07, position: 'absolute', bottom: '-120px', left: '-100px' }}>
     <defs>
      <linearGradient id="testingBlobGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: '#6f42c1', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: '#00a0e9', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    <path fill="url(#testingBlobGradient2)" d="M50.9,-57.9C65.9,-47.7,78.1,-33.5,81.2,-17.2C84.3,-0.9,78.3,17.6,67.3,32.8C56.3,48,40.3,59.9,23.3,66.6C6.2,73.3,-11.9,74.8,-28.7,69.8C-45.5,64.8,-61,53.3,-69.5,38.3C-77.9,23.3,-79.3,4.8,-74.7,-12.6C-70.1,-30,-59.5,-46.3,-45.7,-56.6C-31.9,-66.9,-14.9,-71.2,2.4,-73.2C19.7,-75.2,35.9,-68.1,50.9,-57.9Z" transform="translate(100 100) scale(0.9)" />
  </svg>
);

// Helper for hover styles (can be reused across components)
const getHoverStyles = (isHovered, baseStyle, hoverStyle) => {
  return isHovered ? { ...baseStyle, ...hoverStyle } : baseStyle;
};

const QuickTestingSection = () => {
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const [isImageHovered, setIsImageHovered] = useState(false);

  // GSAP animation refs
  const sectionRef = useRef(null);
  const titleRef = useRef(null);
  const contentRef = useRef(null);
  const imageRef = useRef(null);
  const backgroundRef = useRef(null);

  const imageContainerBaseStyle = {
    borderRadius: '1rem', // Softer, larger radius (Bootstrap rounded-4)
    overflow: 'hidden',
    boxShadow: '0 0.5rem 1rem rgba(0, 0, 0, 0.1)', // Softer, more modern shadow
    transition: 'transform 0.35s ease-out, box-shadow 0.35s ease-out',
    transform: 'scale(1)',
  };

  const imageContainerHoverStyle = {
    transform: 'scale(1.03) translateY(-5px)',
    boxShadow: '0 1rem 2rem rgba(0, 86, 179, 0.15)', // A bit more lift and color hint
  };

  const imageStyle = {
    transition: 'transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)', // For a smoother scale if applied directly
    height: 'auto', // Maintain aspect ratio
    // transform: isImageHovered ? 'scale(1.05)' : 'scale(1)', // Alternative: scale image inside static container
  };

  const ctaButtonBaseStyle = {
    padding: '0.9rem 2.2rem', // Adjusted padding
    fontSize: '1.05rem',
    background: 'linear-gradient(95deg, #0056b3, #007bff)', // Slightly adjusted gradient
    border: 'none',
    boxShadow: '0 5px 15px rgba(0, 86, 179, 0.25)',
    transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
    transform: 'translateY(0)',
  };

  const ctaButtonHoverStyle = {
    background: 'linear-gradient(95deg, #004a99, #0069d9)', // Darken on hover
    boxShadow: '0 8px 20px rgba(0, 86, 179, 0.35)',
    transform: 'translateY(-4px) scale(1.02)', // More pronounced lift
  };

  // GSAP scroll-triggered animations (from ServicesSection)
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.from(titleRef.current, {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: titleRef.current,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Content stagger animation
      gsap.from(contentRef.current, {
        y: 80,
        opacity: 0,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Image animation
      gsap.from(imageRef.current, {
        x: 100,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });

      // Background parallax effect
      gsap.to(backgroundRef.current, {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section
      ref={sectionRef}
      className="section-padding relative overflow-hidden"
      id="quick-testing-section"
      style={{
        background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)',
        backdropFilter: 'blur(10px)'
      }}
    >
      {/* Enhanced Background Elements */}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        {/* Animated Grid Pattern */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            backgroundImage: `
              linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
          }}
        />

        {/* Floating Tech Icons */}
        {[
          { lordicon: 'https://cdn.lordicon.com/qhviklyi.json', top: '10%', left: '5%', delay: 0 },
          { lordicon: 'https://cdn.lordicon.com/kiynvdns.json', top: '20%', right: '8%', delay: 1 },
          { lordicon: 'https://cdn.lordicon.com/hwjcdycb.json', bottom: '15%', left: '3%', delay: 2 },
          { lordicon: 'https://cdn.lordicon.com/qhgmphtg.json', bottom: '25%', right: '5%', delay: 3 }
        ].map((item, index) => (
          <div
            key={index}
            className="absolute w-15 h-15 bg-makonis-secondary/10 rounded-full flex items-center justify-center backdrop-blur-sm border border-makonis-secondary/20 animate-float"
            style={{
              ...item,
              animationDelay: `${item.delay}s`
            }}
          >
            <lord-icon
              src={item.lordicon}
              trigger="hover"
              colors="primary:#00a0e9"
              style={{ width: '24px', height: '24px' }}>
            </lord-icon>
          </div>
        ))}

        <ModernBlob1 />
        <ModernBlob2 />
      </div>
      <div className="container-makonis relative z-10">
        <div ref={titleRef} className="text-center mb-16 lg:mb-24">
          {/* Enhanced Badge */}
          

          <h2 style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)',
            paddingBottom: '1.5rem'
          }}>
            Streamlined Testing Solutions
          </h2>

          <p className="text-xl text-white/90 leading-relaxed mx-auto mb-4 max-w-3xl">
            Traditional unit tests meticulously examine every code piece. You provide inputs, assert outputs, but sometimes test cases are limited, and dedicated, lengthy testing cycles aren't feasible.
          </p>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          <div ref={contentRef} className="lg:pr-8">
            <p className="text-xl leading-relaxed text-white/80 mb-6">
              This conventional method can overly focus on internal application workings. We offer a smarter way: test only what's necessary. Hire a testing engineer on your terms—even for a single test case. That's our flexibility promise.
            </p>
            <p className="text-xl leading-relaxed text-white/80 mb-8">
              Imagine verifying your application's expected behavior without getting bogged down in implementation details. We make that a reality.
            </p>

            {/* Call to Action */}
           
          </div>
          <div ref={imageRef}>
            <div
              className="rounded-2xl overflow-hidden shadow-2xl transition-all duration-500 hover:scale-105 hover:-translate-y-2"
              onMouseEnter={() => setIsImageHovered(true)}
              onMouseLeave={() => setIsImageHovered(false)}
              style={{
                boxShadow: isImageHovered
                  ? '0 25px 50px rgba(0, 86, 179, 0.15)'
                  : '0 10px 25px rgba(0, 0, 0, 0.1)'
              }}
            >
               <img
                src={Home8}
                alt="Software testing process"
                className="w-full h-auto transition-transform duration-500"
              />
            </div>
            <p className="text-center mt-4 text-white/70 text-sm italic">
              Our testing approach adapts to your unique project requirements.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default QuickTestingSection;