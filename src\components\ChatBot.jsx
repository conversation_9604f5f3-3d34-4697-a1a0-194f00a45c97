import React, { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import '@fortawesome/fontawesome-free/css/all.min.css';

const ChatBot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi there, I’m an AI Agent from Makonis, if you have any questions just let me know. Saw that you’re interested in our products/services. I’m available if you have any questions or need help",
      sender: 'bot',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const chatPopupRef = useRef(null);
  const chatIconRef = useRef(null);
  const messagesEndRef = useRef(null);
  const welcomeMessageRef = useRef(null);

  useEffect(() => {
    if (isOpen && chatPopupRef.current) {
      gsap.fromTo(chatPopupRef.current,
        { opacity: 0, scale: 0.8, y: 20 },
        { opacity: 1, scale: 1, y: 0, duration: 0.3, ease: "back.out(1.7)" }
      );
    }
  }, [isOpen]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  useEffect(() => {
    if (chatIconRef.current) {
      gsap.to(chatIconRef.current, {
        scale: 1.1,
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut"
      });
    }
  }, []);

  // Welcome message logic
  useEffect(() => {
    const hasShownWelcome = sessionStorage.getItem('makonisWelcomeShown');

    if (!hasShownWelcome) {
      const timer = setTimeout(() => {
        setShowWelcomeMessage(true);
        if (welcomeMessageRef.current) {
          gsap.fromTo(welcomeMessageRef.current,
            { opacity: 0, scale: 0.8, x: 20 },
            { opacity: 1, scale: 1, x: 0, duration: 0.5, ease: "back.out(1.7)" }
          );
        }
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, []);

  const closeWelcomeMessage = () => {
    if (welcomeMessageRef.current) {
      gsap.to(welcomeMessageRef.current, {
        opacity: 0,
        scale: 0.8,
        x: 20,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          setShowWelcomeMessage(false);
          sessionStorage.setItem('makonisWelcomeShown', 'true');
        }
      });
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const handleSendMessage = () => {
    if (inputMessage.trim()) {
      const newMessage = {
        id: messages.length + 1,
        text: inputMessage,
        sender: 'user',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, newMessage]);
      setInputMessage('');

      setTimeout(() => {
        const botResponse = {
          id: messages.length + 2,
          text: getBotResponse(inputMessage),
          sender: 'bot',
          timestamp: new Date()
        };
        setMessages(prev => [...prev, botResponse]);
      }, 1000);
    }
  };

  const handleQuickReply = (text) => {
    setInputMessage(text);
    handleSendMessage();
  };

  const getBotResponse = () => {
    return "Thank you for your message. We will get back to you soon.";
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Welcome Message */}
      {showWelcomeMessage && !isOpen && (
        <div
          ref={welcomeMessageRef}
          className="position-fixed"
          style={{
            bottom: '90px',
            right: '20px',
            maxWidth: '320px',
            width: 'calc(100% - 40px)',
            background: '#ffffff',
            borderRadius: '16px',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.15)',
            zIndex: 9997,
            border: '1px solid #e9ecef',
            overflow: 'hidden'
          }}
        >
          <div className="p-3">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <div className="d-flex align-items-center">
                <div
                  style={{
                    width: '36px',
                    height: '36px',
                    borderRadius: '50%',
                    background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    marginRight: '12px'
                  }}
                >
                  <i className="fas fa-robot text-white" style={{ fontSize: '16px' }}></i>
                </div>
                <span style={{ fontWeight: '600', color: '#002956', fontSize: '15px' }}>
                  Makonis AI Assistant
                </span>
              </div>
              <button
                onClick={closeWelcomeMessage}
                style={{
                  background: 'transparent',
                  border: 'none',
                  color: '#adb5bd',
                  cursor: 'pointer',
                  padding: '4px',
                  fontSize: '18px',
                  lineHeight: '1'
                }}
                aria-label="Close welcome message"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
            <p style={{
              margin: '0',
              color: '#495057',
              fontSize: '14px',
              lineHeight: '1.5',
              fontWeight: '400'
            }}>
              Hi there! Interested in our products or services? I'm here to answer your questions.
            </p>
          </div>
          {/* Arrow pointing to chat icon */}
          <div
            style={{
              position: 'absolute',
              bottom: '-10px',
              right: '22px',
              width: '0',
              height: '0',
              borderLeft: '10px solid transparent',
              borderRight: '10px solid transparent',
              borderTop: '10px solid #ffffff',
              filter: 'drop-shadow(0 4px 6px rgba(0,0,0,0.1))'
            }}
          ></div>
        </div>
      )}

      {isOpen && (
        <div
          ref={chatPopupRef}
          className="position-fixed"
          style={{
            bottom: '100px',
            right: '20px',
            width: '350px',
            height: '500px',
            background: '#f6f9fc',
            borderRadius: '20px',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)',
            zIndex: 9998,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Header */}
          <div style={{ position: 'relative' }}>
            <div
              className="text-white"
              style={{
                background: 'linear-gradient(135deg, #002244 0%, #0066cc 100%)',
                borderTopLeftRadius: '20px',
                borderTopRightRadius: '20px',
                padding: '16px'
              }}
            >
              <div className="d-flex align-items-center mb-1">
                <div style={{
                  width: '30px',
                  height: '30px',
                  borderRadius: '50%',
                  background: '#fff',
                  marginRight: '10px'
                }}></div>
                <div style={{ flexGrow: 1 }}>
                  <div style={{ fontSize: '14px' }}>Chat with</div>
                  <div style={{ fontWeight: 'bold' }}>Makonis AI Assistant</div>
                </div>
                <div onClick={toggleChat} style={{ cursor: 'pointer' }}>
                  <i className="fas fa-chevron-down text-white"></i>
                </div>
              </div>
            </div>

            {/* Wave */}
            <svg viewBox="0 0 500 50" preserveAspectRatio="none" style={{ display: 'block', width: '100%', height: '30px' }}>
              <path
                d="M0,20 C150,60 350,0 500,30 L500,00 L0,0 Z"
                style={{ fill: 'url(#gradient)' }}
              ></path>
              <defs>
                <linearGradient id="gradient" x1="0" x2="1" y1="0" y2="0">
                  <stop offset="0%" stopColor="#002244" />
                  <stop offset="100%" stopColor="#0066cc" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          {/* Messages */}
          <div className="flex-grow-1 p-3" style={{ overflowY: 'auto' }}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`mb-3 d-flex ${message.sender === 'user' ? 'justify-content-end' : 'justify-content-start'}`}
              >
                <div
                  className="px-3 py-2"
                  style={{
                    maxWidth: '80%',
                    borderRadius: '20px',
                    background: message.sender === 'user'
                      ? 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)'
                      : '#e0e7ef',
                    color: message.sender === 'user' ? '#fff' : '#333',
                    fontSize: '0.9rem'
                  }}
                >
                  {message.text}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Replies */}
          <div className="px-3 pb-2 d-flex gap-2">
            {['Yes', 'No', 'Help'].map((text, idx) => (
              <button
                key={idx}
                className="btn btn-outline-primary btn-sm"
                style={{
                  borderRadius: '15px',
                  fontSize: '0.8rem',
                  padding: '5px 10px'
                }}
                onClick={() => handleQuickReply(text)}
              >
                {text}
              </button>
            ))}
          </div>

          {/* Input */}
          <div className="p-3" style={{ borderTop: '1px solid #eee' }}>
            <div className="d-flex align-items-center">
              <input
                type="text"
                placeholder="Enter your message..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                style={{
                  flex: 1,
                  border: 'none',
                  outline: 'none',
                  padding: '10px',
                  fontSize: '0.9rem',
                  background: 'transparent'
                }}
              />
              <i
                className="fab fa-telegram-plane"
                style={{
                  color: '#0066cc',
                  fontSize: '2.2rem',
                  cursor: 'pointer',
                  marginLeft: '8px'
                }}
                onClick={handleSendMessage}
              />
            </div>
          </div>
        </div>
      )}

      {/* Chat Icon */}
      <div
        ref={chatIconRef}
        onClick={toggleChat}
        className="position-fixed d-flex align-items-center justify-content-center"
        style={{
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          background: 'linear-gradient(135deg, #00a0e9 0%, #0056b3 100%)',
          borderRadius: '50%',
          cursor: 'pointer',
          zIndex: 9999,
          boxShadow: '0 8px 25px rgba(0, 160, 233, 0.4)',
          border: '3px solid rgba(255, 255, 255, 0.2)'
        }}
      >
        <i className={`fas ${isOpen ? 'fa-times' : 'fa-comments'} text-white`} style={{ fontSize: '1.5rem' }} />
      </div>
    </>
  );
};

export default ChatBot;