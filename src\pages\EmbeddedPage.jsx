import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Card, Button, Badge, ProgressBar } from 'react-bootstrap';
import 'animate.css';
import Embedded1 from "../Asserts/Embedded1.jpg";
import Embedded2 from "../Asserts/Embedded2.jpg";
import Embedded3 from "../Asserts/Embedded3.jpg";
import Embedded4 from "../Asserts/Embedded4.jpg";
import Embedded5 from "../Asserts/Embedded5.jpg";
import Embedded6 from "../Asserts/Embedded6.jpg";
import Embedded7 from "../Asserts/Embedded7.png";
import Embedded8 from "../Asserts/Embedded8.jpg";

// Define the page data structure
const embeddedPageData = {
  hero: {
    title: "Embedded Systems",
    subtitle: "We combine in-depth industry expertise with world-class technical knowledge to help you create compelling, high-performance software-based products that power the devices of tomorrow. From concept to deployment, we deliver robust, scalable, and secure solutions that keep you ahead of the competition and meet the demands of a rapidly evolving technological landscape.",
    backgroundImage: 'https://images.unsplash.com/photo-1580584126903-c17d41830450?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=cover&w=1739&q=80'
  },
  intro: {
    title: "Powering the Future of Connected Devices",
    description: "In an era where the world is moving forward with open source and high-performance hardware systems, Makonis is focused on delivering world-class embedded solutions that bridge the gap between hardware and software, creating intelligent systems that drive innovation across industries.",
    image: Embedded1,
  },
  services: [
    {
      id: "telematics",
      title: "Informatics & Telematics",
      icon: "fa-satellite-dish",
      description: "Our telematics solutions enable real-time data collection, analysis, and communication between vehicles, infrastructure, and cloud platforms, providing actionable insights and enhanced control.",
      features: [
        "End-to-end solutions for product testing",
        "Linux/Android and Web-based solutions",
        "Platform and System Validation & Testing",
        "Interoperability Testing",
        "Integration of 3rd party Telematics components",
        "Connected vehicle solutions"
      ],
      image: Embedded2,
      color: "#4158D0"
    },
    {
      id: "chassis",
      title: "Body & Chassis Electronics",
      icon: "fa-car",
      description: "From body control modules to chassis systems, we develop and test electronic control units that ensure optimal performance, safety, and comfort in modern vehicles.",
      features: [
        "ECU Development and calibration",
        "MIL/HIL Simulation and Testing",
        "Cluster Development & Testing",
        "Model-based AUTOSAR compliant design",
        "Auto-code generation",
        "Vehicle network systems"
      ],
      image: Embedded3,
      color: "#C850C0"
    },
    {
      id: "application",
      title: "Application Development",
      icon: "fa-microchip",
      description: "Our embedded application development services create specialized software that runs on microcontrollers and microprocessors, optimized for performance, reliability, and resource efficiency.",
      features: [
        "Real-time operating systems (RTOS)",
        "Firmware development",
        "Device drivers",
        "Bootloaders and BSPs",
        "Low-power optimization",
        "Memory-constrained applications"
      ],
      image: Embedded4,
      color: "#43cea2"
    },
    {
      id: "safety",
      title: "Safety & Systems",
      icon: "fa-shield-alt",
      description: "We develop and validate safety-critical systems that protect lives and assets, ensuring compliance with the highest industry standards and regulatory requirements.",
      features: [
        "Airbag systems",
        "Occupant protection (Seat belts)",
        "Brake Systems",
        "Steering Control",
        "Vehicle Warning Systems",
        "Lane Departure Protection",
        "Pedestrian Detection"
      ],
      image: Embedded5,
      color: "#F37335"
    },
    {
      id: "hardware",
      title: "Hardware Validation",
      icon: "fa-microchip",
      description: "Our hardware validation services ensure that your electronic systems meet specifications, perform reliably under all conditions, and integrate seamlessly with software components.",
      features: [
        "Hardware-in-the-Loop (HIL) Testing",
        "VHDL and Verilog validation",
        "Signal integrity analysis",
        "Power consumption optimization",
        "Thermal analysis",
        "EMC/EMI testing"
      ],
      image: Embedded6,
      color: "#3494E6"
    },
    {
      id: "testing",
      title: "Embedded Testing Services",
      icon: "fa-vial",
      description: "Our comprehensive testing services validate functionality, performance, and reliability of embedded systems, ensuring they operate flawlessly in their intended environments.",
      features: [
        "Unit Testing",
        "Integration Testing",
        "System Testing",
        "Performance Testing",
        "Security Testing",
        "Compliance Testing"
      ],
      image: Embedded7,
      color: "#FF416C"
    },
    {
      id: "software",
      title: "Software Design",
      icon: "fa-code",
      description: "Our software design services create efficient, reliable, and maintainable code that powers embedded systems across various platforms and architectures.",
      features: [
        "Python scripting",
        "Android development",
        "Linux systems",
        "AUTOSAR architecture",
        "C/C++ programming",
        "Embedded Java"
      ],
      image: Embedded8,
      color: "#6f42c1"
    }
  ],
  industries: [
    { name: "Automotive", icon: "fa-car" },
    { name: "Industrial Automation", icon: "fa-cogs" },
    { name: "Consumer Electronics", icon: "fa-tablet-alt" },
    { name: "Medical Devices", icon: "fa-heartbeat" },
    { name: "Aerospace", icon: "fa-plane" },
    { name: "Telecommunications", icon: "fa-signal" }
  ],
  technologies: {
    title: "Technologies & Platforms",
    description: "We leverage cutting-edge technologies and platforms to deliver embedded solutions that meet the demands of today's connected world.",
    categories: [
      {
        name: "Microcontrollers & Processors",
        items: ["ARM Cortex-M", "PIC", "AVR", "STM32", "ESP32", "MSP430", "FPGA"]
      },
      {
        name: "Operating Systems & RTOS",
        items: ["FreeRTOS", "Zephyr", "Embedded Linux", "VxWorks", "QNX", "Android Embedded", "Bare-metal"]
      },
      {
        name: "Communication Protocols",
        items: ["CAN", "LIN", "FlexRay", "Ethernet", "Bluetooth", "Wi-Fi", "LoRa", "Zigbee", "USB", "SPI", "I2C"]
      },
      {
        name: "Development Tools & IDEs",
        items: ["MATLAB/Simulink", "IAR Embedded Workbench", "Keil MDK", "Eclipse CDT", "Arduino IDE", "PlatformIO", "Docker", "Git"]
      }
    ]
  }
};

// Reusable Heading Component
const PageSectionHeading = ({ title, as: Component = 'h2', className = '' }) => (
  <Component
    className={`display-5 fw-bold text-center ${className}`}
    style={{
      fontSize: "3.6rem",
      fontWeight: "800",
      letterSpacing: "2.6px",
      background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
      WebkitBackgroundClip: "text",
      WebkitTextFillColor: "transparent",
      backgroundClip: "text",
      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
    }}
  >
    {title}
  </Component>
);

const EmbeddedPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [animatedElements, setAnimatedElements] = useState({});
  const elementsRef = useRef({});

  // Style constants
  const primaryColor = '#00a0e9';
  const primaryRgb = '0,160,233';

  // Set up intersection observer for animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setAnimatedElements((prev) => ({
              ...prev,
              [entry.target.id]: true
            }));
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe all elements with refs
    Object.keys(elementsRef.current).forEach((key) => {
      if (elementsRef.current[key]) {
        observer.observe(elementsRef.current[key]);
      }
    });

    return () => {
      Object.keys(elementsRef.current).forEach((key) => {
        if (elementsRef.current[key]) {
          observer.unobserve(elementsRef.current[key]);
        }
      });
    };
  }, []);

  return (
    <div
      className="embedded-page overflow-hidden min-h-screen relative"
      style={{
        background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* Animated Background Icons */}
        {[
          {
            icon: "fa-microchip",
            top: "10%",
            left: "5%",
            delay: 0,
          },
          {
            icon: "fa-cogs",
            top: "20%",
            right: "8%",
            delay: 1,
          },
          {
            icon: "fa-car",
            bottom: "15%",
            left: "3%",
            delay: 2,
          },
          {
            icon: "fa-shield-alt",
            bottom: "25%",
            right: "10%",
            delay: 3,
          },
        ].map((item, index) => (
          <div
            key={index}
            className="position-absolute"
            style={{
              top: item.top,
              left: item.left,
              right: item.right,
              bottom: item.bottom,
              animation: `float 6s ease-in-out infinite`,
              animationDelay: `${item.delay}s`,
              opacity: 0.1,
            }}
          >
            <i
              className={`fas ${item.icon} text-white`}
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        ))}
      </div>
      <div className="relative z-10">
      {/* Hero Section */}
      <section
        className="embedded-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url(${embeddedPageData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Embedded Icons */}
          {[
            { icon: "fa-microchip", top: "15%", left: "10%", delay: 0 },
            { icon: "fa-cogs", top: "25%", right: "15%", delay: 1 },
            { icon: "fa-car", bottom: "20%", left: "8%", delay: 2 },
            { icon: "fa-shield-alt", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
                background: "rgba(0, 160, 233, 0.1)", // Consistent background for icons
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  fontSize: "24px",
                  color: "#00a0e9",
                  textShadow: "0 0 10px rgba(0, 160, 233, 0.5)",
                }}
              ></i>
            </div>
          ))}

          {/* Pulse Circles */}
          {[
            { size: "200px", top: "10%", right: "20%", delay: "0s" },
            { size: "150px", bottom: "15%", left: "15%", delay: "2s" },
            { size: "100px", top: "60%", right: "10%", delay: "4s" },
          ].map((circle, index) => (
            <div
              key={index}
              className="position-absolute rounded-circle"
              style={{
                width: circle.size,
                height: circle.size,
                top: circle.top,
                bottom: circle.bottom,
                left: circle.left,
                right: circle.right,
                background: "rgba(0, 160, 233, 0.05)",
                border: "1px solid rgba(0, 160, 233, 0.1)",
                animation: `pulse-effect 4s ease-in-out infinite`,
                animationDelay: circle.delay,
              }}
            ></div>
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <PageSectionHeading title={embeddedPageData.hero.title} as="h1" className="animate__animated animate__fadeInDown animate__slow" />
          <p
            className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "1.35rem",
            }}
          >
            {embeddedPageData.hero.subtitle}
          </p>
        </Container>
      </section>

      {/* Intro Section */}
      <section
        className="py-5 py-md-6"
        id="intro-section"
        ref={(el) => (elementsRef.current['intro-section'] = el)}
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {/* Centered Heading Above Content */}
          <Row className="mb-3">
            <Col xs={12}>
              <PageSectionHeading className='pb-2' title={embeddedPageData.intro.title} />
            </Col>
          </Row>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative mb-4">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>

          <Row className="align-items-center g-4 g-lg-5">
            <Col lg={6} md={10} className="pe-lg-4">
              <div className="content-wrapper">
                <p
                  className="mb-3 mb-md-4"
                  style={{
                    fontSize: "1.2rem",
                    lineHeight: "1.7",
                    color: "rgba(255, 255, 255, 0.9)",
                    textAlign: "justify",
                  }}
                >
                  {embeddedPageData.intro.description}
                </p>

                <div className="d-flex flex-wrap gap-2 mt-4">
                  {["Automotive", "IoT", "Industrial", "Consumer Electronics"].map((badge, index) => (
                    <span
                      key={index}
                      className="badge"
                      style={{
                        background: "rgba(0, 160, 233, 0.3)",
                        color: "white",
                        padding: "0.6rem 1.2rem",
                        borderRadius: "25px",
                        fontSize: "0.9rem",
                        border: "1px solid rgba(0, 160, 233, 0.5)"
                      }}
                    >
                      {badge}
                    </span>
                  ))}
                </div>
              </div>
            </Col>
            <Col lg={6} md={10} className="ps-lg-4">
              <div
                className="image-container"
                style={{
                  borderRadius: "1.25rem",
                  overflow: "hidden",
                  boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                  transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                  backgroundColor: "#f0f2f5",
                }}
              >
                <img
                  src={embeddedPageData.intro.image}
                  alt="Embedded Systems Development"
                  style={{
                    width: "100%",
                    height: "100%",
                    minHeight: "400px",
                    objectFit: "cover",
                    transition: "transform 0.6s ease",
                  }}
                />
              </div>
            </Col>
          </Row>
        </Container>
      </section>

      {/* Industries Section */}
      <section
        className="py-5 py-md-6"
        id="industries-section"
        ref={(el) => (elementsRef.current['industries-section'] = el)}
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {/* Centered Heading Above Content */}
          <Row className="mb-4">
            <Col xs={12}>
              <PageSectionHeading title="Embedded Solutions Across Industries" />
            </Col>
          </Row>

          {/* Enhanced Accent Line */}
          <div className="w-30 h-1 mx-auto relative mb-4">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>

          <div className="text-center mb-4">
            <p
              className="lead mx-auto"
              style={{
                maxWidth: '800px',
                fontSize: "1.3rem",
                lineHeight: "1.7",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              Our embedded systems expertise spans multiple industries, delivering tailored solutions for diverse applications.
            </p>
          </div>

          <Row className="g-4 justify-content-center">
            {embeddedPageData.industries.map((industry, index) => (
              <Col xs={6} sm={4} md={3} lg={2} key={index} className="d-flex">
                <div
                  className={`industry-card p-3 rounded-4 flex-fill d-flex flex-column align-items-center justify-content-center ${animatedElements['industries-section'] ? 'animate__animated animate__fadeInUp' : ''}`}
                  style={{
                    animationDelay: `${index * 0.1}s`,
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(20px)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    minHeight: '140px',
                    textAlign: 'center',
                    cursor: 'pointer'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-8px)';
                    e.currentTarget.style.boxShadow = '0 10px 20px rgba(0, 160, 233, 0.3)';
                    e.currentTarget.style.background = 'rgba(0, 160, 233, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  <i
                    className={`fas ${industry.icon} mb-2`}
                    style={{
                      fontSize: '2.5rem',
                      color: '#00a0e9',
                      textShadow: '0 0 15px rgba(0, 160, 233, 0.5)'
                    }}
                  ></i>
                  <h4 className="fw-bold mb-0 text-white" style={{ fontSize: '1.1rem' }}>{industry.name}</h4>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
      </section>

      {/* Services Section */}
      <div
        className="py-5 py-md-6"
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {embeddedPageData.services.map((service, idx) => (
            <section
              key={service.id}
              className="mb-5 mb-md-6 py-3"
              id={`${service.id}-section`}
              ref={(el) => (elementsRef.current[`${service.id}-section`] = el)}
            >
              {/* Centered Heading Above Content */}
              <Row className="mb-4">
                <Col xs={12}>
                  <PageSectionHeading className='pb-2' title={service.title} />
                </Col>
              </Row>

              {/* Enhanced Accent Line */}
              <div className="w-30 h-1 mx-auto relative mb-4">
                <div
                  className="w-full h-full rounded-sm shadow-glow"
                  style={{
                    background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                  }}
                />
              </div>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  idx % 2 === 1 ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={10}
                  className={`${idx % 2 === 1 ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    <p
                      className="mb-3 mb-md-4"
                      style={{
                        fontSize: "1.2rem",
                        lineHeight: "1.7",
                        color: "rgba(255, 255, 255, 0.9)",
                        textAlign: "justify",
                      }}
                    >
                      {service.description}
                    </p>
                    <ul
                      className="list-unstyled mt-3"
                      style={{
                        fontSize: "1.2rem",
                        lineHeight: "1.6",
                        color: "rgba(255, 255, 255, 0.9)",
                      }}
                    >
                      {service.features.map((feature, index) => (
                        <li
                          key={index}
                          className="d-flex align-items-start py-2"
                        >
                          <div
                            className="me-3 d-flex align-items-center justify-content-center"
                            style={{
                              width: "35px",
                              height: "35px",
                              borderRadius: "50%",
                              background: "rgba(0, 160, 233, 0.2)",
                              border: "1px solid rgba(0, 160, 233, 0.3)",
                              flexShrink: 0,
                            }}
                          >
                            <i
                              className="fas fa-check"
                              style={{
                                color: "#00a0e9",
                                fontSize: "16px",
                              }}
                            ></i>
                          </div>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </Col>
                <Col lg={6} md={10} className={`${idx % 2 === 1 ? "pe-lg-4" : "ps-lg-4"}`}>
                  <div
                    className="image-container"
                    style={{
                      borderRadius: "1.25rem",
                      overflow: "hidden",
                      boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                      transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                      backgroundColor: "#f0f2f5",
                    }}
                  >
                    <img
                      src={service.image}
                      alt={service.title}
                      style={{
                        width: "100%",
                        height: "100%",
                        minHeight: "400px",
                        objectFit: "cover",
                        transition: "transform 0.6s ease",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

      {/* Technologies Section */}
      <section
        className="py-5 py-md-6"
        id="tech-section"
        ref={(el) => (elementsRef.current['tech-section'] = el)}
        style={{
          background: "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <Row className="mb-4">
            <Col xs={12}>
              <PageSectionHeading className='pb-2' title={embeddedPageData.technologies.title} />
            </Col>
          </Row>

          <div className="w-30 h-1 mx-auto relative mb-4">
            <div
              className="w-full h-full rounded-sm shadow-glow"
              style={{
                background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
              }}
            />
          </div>

          <div className="text-center mb-5">
            <p
              className="lead mx-auto"
              style={{
                maxWidth: '800px',
                fontSize: "1.2rem",
                lineHeight: "1.7",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              {embeddedPageData.technologies.description}
            </p>
          </div>

          <Row className="g-4">
            {embeddedPageData.technologies.categories.map((category, catIndex) => {
              let categoryIcon = 'fa-tools';
              switch (category.name) {
                case 'Microcontrollers & Processors':
                  categoryIcon = 'fa-microchip';
                  break;
                case 'Operating Systems & RTOS':
                  categoryIcon = 'fa-cogs';
                  break;
                case 'Communication Protocols':
                  categoryIcon = 'fa-network-wired';
                  break;
                case 'Development Tools & IDEs':
                  categoryIcon = 'fa-laptop-code';
                  break;
                default:
                  categoryIcon = 'fa-tools';
              }

              return (
                <Col md={6} key={catIndex}>
                  <div
                    className={`tech-category p-4 rounded-4 h-100 d-flex flex-column ${animatedElements['tech-section'] ? 'animate__animated animate__fadeInUp' : ''}`}
                    style={{
                      animationDelay: `${catIndex * 0.15}s`,
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-10px)';
                      e.currentTarget.style.boxShadow = '0 15px 30px rgba(0, 160, 233, 0.3)';
                      e.currentTarget.style.background = 'rgba(0, 160, 233, 0.2)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                    }}
                  >
                    <h3 className="h4 fw-bold mb-4 d-flex align-items-center justify-content-center">
                      <i className={`fas ${categoryIcon} me-3`} style={{ color: primaryColor, fontSize: '1.8rem' }}></i>
                      <span className="text-white">{category.name}</span>
                    </h3>

                    <div className="flex-grow-1">
                      <Row className="g-2 justify-content-center">
                        {category.items.map((item, itemIndex) => (
                          <Col xs="auto" key={itemIndex}>
                            <Badge
                              pill
                              className="tech-item px-3 py-2 text-white"
                              style={{
                                background: 'rgb(38 79 140)',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                transition: 'all 0.2s ease',
                                fontSize: '0.9rem',
                                cursor: 'pointer'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.background = primaryColor;
                                e.currentTarget.style.transform = 'translateY(-2px) scale(1.05)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.background = 'rgb(38 79 140)';
                                e.currentTarget.style.transform = 'translateY(0) scale(1)';
                              }}
                            >
                              {item}
                            </Badge>
                          </Col>
                        ))}
                      </Row>
                    </div>
                  </div>
                </Col>
              );
            })}
          </Row>
        </Container>
      </section>

      {/* Global CSS for animations and custom styles */}
      <style>{`
        /* Custom Shadow Glow for Accent Lines */
        .shadow-glow {
          box-shadow: 0 0 15px rgba(0, 160, 233, 0.7);
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          50% {
            transform: translateY(-20px) rotate(10deg);
          }
        }

        @keyframes pulse-effect {
          0%, 100% {
            transform: scale(1);
            opacity: 0.7;
          }
          50% {
            transform: scale(1.15);
            opacity: 1;
          }
        }

        @keyframes flow-animation {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          10% {
            opacity: 0.6;
          }
          90% {
            opacity: 0.6;
          }
          100% {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        @keyframes fade-pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.8;
          }
          50% {
            transform: scale(1.3);
            opacity: 1;
          }
        }

        @keyframes floating-icon-subtle {
          0%, 100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-15px);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }
      `}</style>
      </div>
    </div>
  );
};

export default EmbeddedPage;