import React, { useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Integration1 from "../Asserts/Integration1.png"; // Consider renaming this asset for clarity in a business context, e.g., 'BusinessSolutions.png'

// It's good practice to register plugins at the top level
gsap.registerPlugin(ScrollTrigger);

const BusinessModelPage = () => {
    // --- DATA --- //

    const engagementModels = [
        { title: "Staff Augmentation" },
        { title: "Managed T&M" },
        { title: "Fixed Bid" },
        { title: "Managed Solutions" },
        { title: "COE/ODC" }
    ];

    const benefits = [
        "Faster Time to Market",
        "Global Teams for Cost Efficiency & Enhanced Business Value",
        "Minimized Business & Operational Risks",
        "Improved Business Agility",
        "Reduced Total Cost of Ownership",
        "Efficient & Adaptive Resource Management"
    ];

    // Updated data: services instead of competencies, with descriptions refined for business focus
    const services = [
        {
            name: "Strategy & Consulting",
            icon: "fas fa-lightbulb",
            description: "Guiding your digital journey with strategic insights and actionable roadmaps."
        },
        {
            name: "Custom Software Development",
            icon: "fas fa-laptop-code",
            description: "Crafting bespoke software solutions tailored to your unique business needs."
        },
        {
            name: "Cloud Transformation",
            icon: "fas fa-cloud-upload-alt",
            description: "Migrating and optimizing your infrastructure for scalable cloud environments."
        },
        {
            name: "Data Analytics & AI",
            icon: "fas fa-chart-line",
            description: "Transforming raw data into valuable insights and intelligent automated processes."
        },
        {
            name: "Managed Services",
            icon: "fas fa-handshake",
            description: "Providing ongoing support and maintenance to ensure seamless operations."
        },
        {
            name: "Digital Product Engineering",
            icon: "fas fa-cube",
            description: "Designing and building innovative digital products from concept to launch."
        },
        {
            name: "Cybersecurity Solutions",
            icon: "fas fa-shield-alt",
            description: "Protecting your digital assets with robust security strategies and implementations."
        },
        {
            name: "Quality Assurance & Testing",
            icon: "fas fa-check-circle",
            description: "Ensuring the reliability and performance of your applications through rigorous testing."
        }
    ];

    // A new section with key advantages
    const advantages = [
        {
            icon: "fas fa-users-cog",
            title: "Expert-Led Teams",
            description: "Our teams are comprised of certified professionals and subject matter experts dedicated to your project's success."
        },
        {
            icon: "fas fa-lightbulb",
            title: "Innovative Solutions",
            description: "We leverage the latest technologies and forward-thinking strategies to build solutions that are future-ready."
        },
        {
            icon: "fas fa-shield-alt",
            title: "Proven & Secure Process",
            description: "With a time-tested and security-first approach, we ensure reliable and transparent delivery from start to finish."
        }
    ];


    // --- ANIMATIONS --- //

    useEffect(() => {
        // Hero Section Animation
        gsap.fromTo(".hero-content > *", { opacity: 0, x: -50 }, { opacity: 1, x: 0, duration: 1, stagger: 0.2, ease: "power3.out", delay: 0.5 });
        gsap.fromTo(".hero-image", { opacity: 0, scale: 0.9 }, { opacity: 1, scale: 1, duration: 1.2, ease: "power3.out", delay: 0.3 });

        // Animate Sections on Scroll
        gsap.utils.toArray('.animated-section').forEach(section => {
            gsap.fromTo(section, { opacity: 0, y: 100 }, {
                opacity: 1, y: 0, duration: 1, ease: "power3.out",
                scrollTrigger: { trigger: section, start: "top 85%", toggleActions: "play none none none" }
            });
        });

        // "Step-Up" Models Animation
        if (document.querySelector(".step-item")) {
            gsap.fromTo(".step-item", { opacity: 0, y: 40, x: -40 }, {
                opacity: 1, y: 0, x: 0, duration: 0.8, ease: 'power3.out', stagger: 0.2,
                scrollTrigger: { trigger: '.steps-container', start: 'top 70%' }
            });
        }

        // Service Grid Animation (formerly Competency Grid Animation)
        if (document.querySelector(".service-card")) {
            gsap.fromTo(".service-card", { opacity: 0, y: 50, scale: 0.9 }, {
                opacity: 1, y: 0, scale: 1, duration: 0.5, ease: 'power3.out', stagger: 0.1,
                scrollTrigger: { trigger: '.services-grid', start: 'top 80%' }
            });
        }

    }, []);

    // --- COMPONENT RENDER --- //

    return (
        <div
            className="min-h-screen relative overflow-hidden"
            style={{
                background: 'linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)',
                backdropFilter: "blur(10px)",
            }}
        >
            {/* Background Grid Pattern */}
            <div className="absolute inset-0 z-0">
                <div
                    className="absolute inset-0 opacity-20"
                    style={{
                        backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
                        backgroundSize: "50px 50px",
                    }}
                />
                {/* Animated Background Icons */}
                {[
                    {
                        icon: "fa-chart-line",
                        top: "10%",
                        left: "5%",
                        delay: 0,
                    },
                    {
                        icon: "fa-handshake",
                        top: "20%",
                        right: "8%",
                        delay: 1,
                    },
                    {
                        icon: "fa-lightbulb",
                        bottom: "15%",
                        left: "3%",
                        delay: 2,
                    },
                    {
                        icon: "fa-cogs",
                        bottom: "25%",
                        right: "10%",
                        delay: 3,
                    },
                ].map((item, index) => (
                    <div
                        key={index}
                        className="position-absolute"
                        style={{
                            top: item.top,
                            left: item.left,
                            right: item.right,
                            bottom: item.bottom,
                            animation: `float 6s ease-in-out infinite`,
                            animationDelay: `${item.delay}s`,
                            opacity: 0.1,
                        }}
                    >
                        <i
                            className={`fas ${item.icon} text-white`}
                            style={{ fontSize: "2rem" }}
                        ></i>
                    </div>
                ))}
            </div>
            <div className="relative z-10">

            {/* Hero Section */}
            <section className="flex items-start justify-start py-10 px-4"> {/* MODIFIED: Removed min-h-screen, changed to items-start, justify-start, and py-10 */}
                <div className="container mx-auto grid lg:grid-cols-2 gap-12 items-center">
                    <div className="hero-content text-white">
                        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight" style={{
                            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                            WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text', paddingBottom: '10px'
                        }}>
                            Innovative Business Models for Sustainable Growth
                        </h1>
                        <p className="text-base sm:text-lg text-gray-300 mt-6 leading-relaxed">
                            We partner with you to implement strategic engagement models and deliver comprehensive solutions that drive your business forward.
                        </p>
                    </div>
                    <div className="hero-image flex justify-center items-center">
                        <img src={Integration1} alt="Abstract network of digital connections" className="rounded-2xl shadow-2xl w-full h-auto object-cover max-h-[500px]" />
                    </div>
                </div>
            </section>

            {/* Our Flexible Engagement Models Section */}
            <section className="py-12 animated-section"> {/* MODIFIED: Changed py-20 to py-12 */}
                <div className="container mx-auto px-4">
                    <div className="text-center mb-16">
                        <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold" style={{
                            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',paddingBottom: '6px',
                            WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
                        }}>Our Flexible Engagement Models</h2>
                        <p className="text-base sm:text-lg text-gray-400 mt-4 max-w-3xl mx-auto">We adapt to your needs, offering a spectrum of models from simple augmentation to full-scale development centers.</p>
                    </div>
                    <div className="grid lg:grid-cols-2 gap-16 items-center">
                        {/* Left side: Visual "Step-Up" representation */}
                        <div className="steps-container relative flex items-end justify-start h-[450px]">
                            {engagementModels.map((model, index) => (
                                <div key={index} className="step-item absolute" style={{ bottom: `${index * 20}%`, left: `${index * 15}%` }}>
                            {index > 0 && (
                                <div className="absolute w-16 h-16 -top-8 -left-8">
                                    <svg viewBox="0 0 100 100" className="w-full h-full">
                                        <path d="M 0,100 Q 50,100 50,50 Q 50,0 100,0" stroke="#00a0e9" strokeDasharray="4 4" strokeWidth="2" fill="none" />
                                    </svg>
                                </div>
                            )}
                            <div className="relative w-48 p-4 rounded-lg bg-[#00a0e9]/10 border border-[#00a0e9]/30 backdrop-blur-sm shadow-md text-center transform hover:scale-105 hover:border-[#00a0e9] transition-all duration-300">
                                <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-white">{model.title}</h3>
                            </div>
                        </div>
 ))}
                    </div>
                    {/* Right side: Benefits */}
                    <div className="p-8 rounded-2xl bg-gray-500/10 border border-gray-400/20 backdrop-blur-lg">
                        <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-white mb-6">Delivering Tangible Business Value</h3>
                        <ul className="space-y-4">
                            {benefits.map((benefit, index) => (
                                <li key={index} className="flex items-start">
                                    <div className="w-6 h-6 rounded-full flex items-center justify-center bg-[#00A0E9]/30 text-[#00A0E9] mr-4 mt-1 flex-shrink-0">
                                        <i className="fas fa-check text-xs"></i>
                                    </div>
                                    <span className="text-sm text-gray-300 leading-relaxed">{benefit}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
        </div>
</section >

    {/* The Makonis Advantage */ }
    < section className = "py-12 animated-section" >
        <div className="container mx-auto px-4">
            <div className="text-center mb-16">
                <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold" style={{
                    background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                    WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
                }}>The Makonis Advantage</h2>
                <p className="text-base sm:text-lg text-gray-400 mt-4 max-w-3xl mx-auto">We combine technology, talent, and process to deliver unparalleled results and a seamless client experience.</p>
            </div>
            <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                {advantages.map((advantage, index) => (
                    <div key={index} className="advantage-card text-center p-8 rounded-xl bg-gray-500/10 border border-gray-400/20 backdrop-blur-lg transform hover:-translate-y-2 transition-transform duration-300">
                        <div className="text-4xl text-[#ff6b35] mb-4">
                            <i className={advantage.icon}></i>
                        </div>
                        <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-white mb-3">{advantage.title}</h3>
                        <p className="text-sm text-gray-400 leading-relaxed">{advantage.description}</p>
                    </div>
                ))}
            </div>
        </div>
 </section >

    {/* Our Service Offerings Section (formerly Core Competencies) */ }
    < section className = "py-12 animated-section" >
        <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-2 pb-3" style={{
                background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text'
            }}>Our Service Offerings</h2>
            <p className="text-base sm:text-lg text-gray-400 max-w-3xl mx-auto mb-16">A comprehensive suite of services to architect, build, and scale modern enterprise solutions that empower your business.</p>
            <div className="services-grid grid grid-cols-2 md:grid-cols-4 gap-8">
                {services.map((service, index) => (
                    <div key={index} className="service-card p-6 bg-[#00a0e9]/5 rounded-xl border border-transparent hover:border-[#00a0e9]/50 hover:bg-[#00a0e9]/10 transition-all duration-300">
                        <div className="text-4xl text-[#00a0e9] mb-5">
                            <i className={service.icon}></i>
                        </div>
                        <h3 className="text-sm sm:text-base lg:text-lg font-semibold text-white mb-2">{service.name}</h3>
                        <p className="text-xs sm:text-sm text-gray-400 leading-snug">{service.description}</p>
                    </div>
                ))}
            </div>
        </div>
      </section >

            </div>
            <style>{`
                @keyframes float {
                    0%, 100% {
                        transform: translateY(0px);
                    }
                    50% {
                        transform: translateY(-20px);
                    }
                }
            `}</style>
    </div >
  );
};

export default BusinessModelPage;