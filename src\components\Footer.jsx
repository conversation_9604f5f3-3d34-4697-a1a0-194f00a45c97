import React from "react";
import { <PERSON> } from "react-router-dom";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../Asserts/Makonis-Logo.png";

const Footer = () => {
  return (
    <footer className="bg-white text-gray-700">
      <div className="max-w-7xl mx-auto px-4 py-5">
        {/* Changed grid to 4 columns on medium screens and up to accommodate Contact Us */}
        {/* Adjusted gap-x-4 to reduce horizontal spacing between columns */}
        {/* Increased ml-[-8] to ml-[-12] to shift the entire grid further to the left, pushing Services right */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-x-4 gap-y-12 ml-[-12]">
          {/* Logo Section - Adjusted pt-4 to align better with headings */}
          <div className="flex flex-col items-center md:items-start pt-4">
            <img
              src={MakonisLogo}
              alt="Makonis Logo"
              className="h-36 md:h-40 mb-4 transition-transform duration-300 hover:scale-105"
            />
            {/* Removed the blue separator below the logo as requested */}
          </div>

          {/* About Us */}
          <div className="md:pt-0">
            <h5 className="text-gray-900 text-xl font-bold mb-6 border-b-2 border-blue-600 pb-2 inline-block">
              ABOUT US
            </h5>
            <ul className="space-y-3 text-lg">
              {[
                { label: "Company", href: "/" },
                { label: "Our Team", href: "/team" },
                { label: "Careers", href: "/careers" },
              ].map((link) => (
                <li key={link.label}>
                  <Link
                    to={link.href}
                    className="hover:text-blue-600 transition-colors duration-300"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services - Positioned correctly by the 4-column grid, now shifted further right */}
          <div className="md:pt-0">
            <h5 className="text-gray-900 text-xl font-bold mb-6 border-b-2 border-blue-600 pb-2 inline-block">
              SERVICES
            </h5>
            <ul className="space-y-3 text-lg">
              {[
                { label: "Artificial Intelligence", to: "/products/ai", icon: "fas fa-robot" },
                { label: "Data Analytics", to: "/analytics", icon: "fas fa-chart-bar" },
                { label: "IoT Solutions", to: "/iot", icon: "fas fa-wifi" },
                { label: "Web & Mobile Dev", to: "/services/web-development", icon: "fas fa-mobile-alt" },
                { label: "Testing Services", to: "/services/testing", icon: "fas fa-bug" },
                { label: "Embedded Systems", to: "/embedded", icon: "fas fa-microchip" },
              ].map((service) => (
                <li key={service.label}>
                  <Link
                    to={service.to}
                    className="hover:text-blue-600 transition-colors duration-300 flex items-center"
                  >
                    <i className={`${service.icon} text-base mr-2 text-blue-500`}></i> {/* Added icon */}
                    {service.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Us Information - Added as the fourth column */}
          <div className="md:pt-0">
            <h5 className="text-gray-900 text-xl font-bold mb-6 border-b-2 border-blue-600 pb-2 inline-block">
              CONTACT US
            </h5>
            <ul className="space-y-3 text-base">
              <li className="flex">
                <i className="fas fa-map-marker-alt text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i>
                <span className="text-gray-700">51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037</span>
              </li>
              <li className="flex">
                <i className="fas fa-envelope text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i>
                <a href="mailto:<EMAIL>" className="text-gray-700 hover:text-blue-600 transition-colors duration-300"><EMAIL></a>
              </li>
              <li className="flex">
                <i className="fas fa-phone-alt text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i>
                <a href="tel:+918041707838" className="text-gray-700 hover:text-blue-600 transition-colors duration-300">+91 8041707838</a>
              </li>
            </ul>
          </div>
        </div>

        {/* Divider above social icons - mt-6 remains, pt-1 remains */}
       <div className="mt-6 pt-1 w-1/2 mx-auto border-t border-gray-400"></div>


        {/* Social Icons - Centered above copyrights, using consistent vertical margin */}
        <div className="flex space-x-6 justify-center my-4">
          {[
            { label: "Facebook", href: "#", icon: "fab fa-facebook-f" },
            { label: "Instagram", href: "#", icon: "fab fa-instagram" },
            { label: "Twitter", href: "#", icon: "fab fa-twitter" },
            { label: "LinkedIn", href: "#", icon: "fab fa-linkedin-in" },
          ].map((social) => (
            <a
              key={social.label}
              href={social.href}
              className="text-gray-500 hover:text-blue-600 transition-colors duration-300"
              aria-label={social.label}
              target="_blank"
              rel="noopener noreferrer"
            >
              <i className={`${social.icon} text-3xl`}></i>
            </a>
          ))}
        </div>

        {/* Original Divider (now above copyrights) - mt-0, pt-1 remains */}
        <div className="mt-0 w-1/2 mx-auto border-t border-gray-400 pt-4 text-center">
          <p className="text-gray-500 text-base">
            © {new Date().getFullYear()} Makonis Software Solutions. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
