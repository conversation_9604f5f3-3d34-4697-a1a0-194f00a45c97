import React, { useEffect, useRef } from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Importing relevant icons for Physical Verification (FaSearch, FaCheckDouble, FaShieldAlt, FaIndustry remain for expertise)
import {
  FaSearch,
  FaCheckDouble,
  FaShieldAlt,
  FaIndustry,
} from "react-icons/fa";

// Import your actual images from the Asserts folder here:
// Using the provided filenames: physical1.png, physical2.png, physical3.png
import Physical1 from "../Asserts/physical4.jpg";
import Physical2 from "../Asserts/physical2.jpg";
import Physical3 from "../Asserts/physical3.jpg";

gsap.registerPlugin(ScrollTrigger);

const accentRgb = "0, 160, 233";
const backgroundColor = "#001a35";

const headingStyle = {
  fontSize: "3rem",
  fontWeight: "800",
  letterSpacing: "2.6px",
  marginBottom: "1rem",
  background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
  WebkitBackgroundClip: "text",
  WebkitTextFillColor: "transparent",
  backgroundClip: "text",
  textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
};

const PhysicalVerificationPage = () => {
  const pageRef = useRef(null);

  const expertiseAreas = [
    {
      icon: FaCheckDouble,
      title: "Sign-off Checks",
      desc: "Comprehensive DRC, LVS, Antenna, and ERC checks to guarantee your layout is 100% compliant with foundry rules.",
    },
    {
      icon: FaShieldAlt,
      title: "Reliability Verification",
      desc: "Advanced EM/IR, ESD, and Latch-up analysis to ensure long-term device robustness and prevent field failures.",
    },
    {
      icon: FaIndustry,
      title: "Foundry Tape-out",
      desc: "Flawless GDSII/OASIS generation and rule deck customization for a smooth hand-off to any foundry.",
    },
    {
      icon: FaSearch,
      title: "Advanced DFM",
      desc: "Applying cutting-edge Design for Manufacturability rules to maximize yield and minimize silicon defects.",
    },
  ];

  // Updated differentiators to use imported image variables with the correct names
  const differentiators = [
    {
      imageUrl: Physical1,
      title: "Zero-Error Tolerance",
      desc: "Our meticulous, automated process ensures clean, error-free layouts, guaranteeing first-pass silicon success and faster time-to-market. This reduces costly re-spins and improves overall design reliability.",
    },
    {
      imageUrl: Physical2,
      title: "Actionable Debugging",
      desc: "We don't just find errors; we provide detailed, actionable reports that accelerate your team's debugging process for any identified issues. Our insights help engineers resolve problems faster and optimize overall design efficiency.",
    },
    {
      imageUrl: Physical3,
      title: "Full Ownership Model",
      desc: "From environment setup to final tape-out, we can take full ownership of the PV flow, freeing up your team to focus on core design tasks. Our comprehensive support ensures seamless project execution and delivery.",
    },
  ];

  const methodologySteps = [
    {
      title: "1. Setup & Integration",
      desc: "Project kick-off involves integrating foundry rule decks, configuring the verification environment, and establishing clear communication channels.",
    },
    {
      title: "2. Iterative Verification",
      desc: "We perform rapid, iterative verification cycles in sync with your design team, catching and resolving issues early in the block-level implementation phase.",
    },
    {
      title: "3. Full-Chip Sign-off",
      desc: "A comprehensive, full-chip verification run is performed on the fully integrated layout, validating the design across all required corners and conditions.",
    },
    {
      title: "4. Tape-out & Handoff",
      desc: "We generate final validation reports, prepare the pristine GDSII/OASIS file, and assemble a complete package for a successful foundry tape-out.",
    },
  ];

  useEffect(() => {
    document.title = "Physical Verification Services | Makonis";
    window.scrollTo(0, 0);

    const sections = gsap.utils.toArray(".animated-section");
    sections.forEach((section, i) => {
      gsap.fromTo(
        section,
        { autoAlpha: 0, y: 50 },
        {
          autoAlpha: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          scrollTrigger: {
            trigger: section,
            start: "top 85%",
            toggleActions: "play none none reverse",
          },
        }
      );
    });
  }, []);

  return (
    <div
      ref={pageRef}
      className="min-h-screen relative overflow-hidden"
      style={{
        background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)",
        backdropFilter: "blur(10px)",
        color: "#e0e0e0"
      }}
    >
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
        {/* Animated Background Icons */}
        {[
          {
            icon: "fa-check-circle",
            top: "10%",
            left: "5%",
            delay: 0,
          },
          {
            icon: "fa-shield-alt",
            top: "20%",
            right: "8%",
            delay: 1,
          },
          {
            icon: "fa-search",
            bottom: "15%",
            left: "3%",
            delay: 2,
          },
          {
            icon: "fa-microchip",
            bottom: "25%",
            right: "10%",
            delay: 3,
          },
        ].map((item, index) => (
          <div
            key={index}
            className="position-absolute"
            style={{
              top: item.top,
              left: item.left,
              right: item.right,
              bottom: item.bottom,
              animation: `float 6s ease-in-out infinite`,
              animationDelay: `${item.delay}s`,
              opacity: 0.1,
            }}
          >
            <i
              className={`fas ${item.icon} text-white`}
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        ))}
      </div>
      <div className="relative z-10">
      {/* Hero Section */}
      <section
        className="d-flex align-items-center justify-content-center position-relative text-center hero-section"
        style={{ minHeight: "90vh", padding: "80px 20px", overflow: "hidden" }}
      >
        <div className="hero-background-overlay"></div>
        <div className="container" style={{ zIndex: 2, maxWidth: "960px" }}>
          <h1
            className="fw-bolder hero-title mb-4"
            style={{
              background: `linear-gradient(135deg, #ffffff 0%, rgba(${accentRgb}, 1) 100%)`,
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              textShadow: `0 0 40px rgba(${accentRgb}, 0.4)`,
              fontSize: "clamp(2.8rem, 6vw, 4.2rem)",
              lineHeight: 1.2,
            }}
          >
            The Final Guardian of Your Silicon.
          </h1>
          <p
            className="lead hero-subtitle mb-4"
            style={{
              color: "#c0c0c0",
              fontSize: "clamp(1.1rem, 2.5vw, 1.6rem)",
              maxWidth: "1000px",
              margin: "0 auto",
            }}
          >
            We provide meticulous, zero-defect Physical Verification services
            that ensure your chip design is manufacturable, reliable, and ready
            for first-pass success.
          </p>
        </div>
      </section>

      {/* Expertise Section - NEW GRID LAYOUT */}
      <section id="expertise" className="py-5 animated-section">
        <div className="container">
          <div className="text-center">
            <h2 style={headingStyle}>Core Verification Areas</h2>
          </div>
          <div className="expertise-grid">
            {expertiseAreas.map((area, index) => {
              const IconComponent = area.icon;
              return (
                <div key={index} className="expertise-card">
                  <div className="expertise-icon-wrapper">
                    <IconComponent size={30} />
                  </div>
                  <h4 className="fw-bold mt-3 mb-2">{area.title}</h4>
                  <p className="mb-0">{area.desc}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Differentiators Section */}
      <section className="py-5 animated-section">
        <div className="container">
          <div className="text-center">
            <h2 style={headingStyle} className="mb-5">Why Partner With Us?</h2>
          </div>
          {differentiators.map((item, idx) => {
            const isEven = idx % 2 === 0;
            return (
              <div key={idx} className="row align-items-center mb-5 gx-5">
                <div
                  className={`col-md-5 ${isEven ? "order-md-1" : "order-md-2"}`}
                >
                  <div className="differentiator-image-box">
                    <img
                      src={item.imageUrl}
                      alt={item.title}
                      className="differentiator-image"
                    />
                  </div>
                </div>
                <div
                  className={`col-md-7 ${isEven ? "order-md-2" : "order-md-1"}`}
                >
                  <div className="differentiator-text">
                    {/* MODIFICATION: Increased font size for the title */}
                    <h1
                      className="fw-bold mb-3"
                      style={{
                        color: `rgba(${accentRgb}, 1)`,
                        fontSize: "2.5rem",
                      }}
                    >
                      {item.title}
                    </h1>
                    {/* MODIFICATION: Increased font size for the description */}
                    <p
                      className="lead"
                      style={{ color: "#aab8c8", fontSize: "1.2rem" }}
                    >
                      {item.desc}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </section>

      {/* Methodology Section */}
      <section
        className="py-5 animated-section"
        style={{ backgroundColor: "rgba(0,0,0,0.15)" }}
      >
        <div className="container">
          <div className="text-center">
            <h2 style={headingStyle}>Our Verification Flow</h2>
            <p
              className="lead mx-auto"
              style={{
                maxWidth: "800px",
                
                marginBottom: "2rem",
              }}
            >
              A proven, systematic approach to ensure first-pass silicon
              success.
            </p>
          </div>
          <div className="row">
            <div className="col-lg-10 mx-auto">
              <div className="methodology-list">
                {methodologySteps.map((step, idx) => (
                  <div key={idx} className="methodology-item">
                    <h4 className="fw-bold">{step.title}</h4>
                    <p className="mb-0">{step.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <style>
        {`
          .hero-background-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 30% 100%, rgba(${accentRgb}, 0.15) 0%, transparent 40%),
                        radial-gradient(circle at 90% 20%, rgba(${accentRgb}, 0.1) 0%, transparent 30%);
            z-index: 1;
          }
          .hero-cta {
            background-color: rgba(${accentRgb}, 1);
            border: none;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
          }
          .hero-cta:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 24px rgba(${accentRgb}, 0.4);
          }
          .hero-title {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeUp 1.2s ease-out forwards;
          }
          .hero-subtitle {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeUp 1.2s ease-out forwards;
            animation-delay: 0.3s;
          }
          @keyframes fadeUp {
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .expertise-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            justify-items: center;
            padding: 2rem;
          }
          .expertise-card {
            background: rgba(0,0,0,0.2);
            border: 1px solid rgba(${accentRgb}, 0.2);
            border-radius: 15px;
            padding: 2rem;
            width: 100%;
            max-width: 320px;
            text-align: center;
            transition: all 0.3s ease;
          }
          .expertise-card:hover {
            transform: translateY(-10px);
            background: rgba(${accentRgb}, 0.1);
            border-color: rgba(${accentRgb}, 0.7);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
          }
          .expertise-icon-wrapper {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(${accentRgb}, 0.1), rgba(${accentRgb}, 0.2));
            color: rgba(${accentRgb}, 1);
            box-shadow: 0 0 20px rgba(${accentRgb}, 0.2);
          }
          .differentiator-image-box {
            width: 100%;
            height: 300px; /* Increased height for better image adjustment */
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(0,0,0,0.2);
            border-radius: 20px;
            border: 1px solid rgba(${accentRgb}, 0.2);
            overflow: hidden;
          }
          .differentiator-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 20px;
            transition: transform 0.3s ease;
          }
          .differentiator-image-box:hover .differentiator-image {
            transform: scale(1.05);
          }
          .differentiator-text {
            padding: 1rem;
          }
          .methodology-list {
            position: relative;
          }
          .methodology-item {
            padding: 1.5rem 1.5rem 1.5rem 2.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            background: rgba(0,0,0,0.2);
            border-left: 4px solid transparent;
            border-radius: 8px;
            transition: all 0.3s ease;
          }
          .methodology-item:hover {
            border-left-color: rgba(${accentRgb}, 1);
            transform: translateX(10px);
            background: rgba(${accentRgb}, 0.1);
          }
          .methodology-item h4 {
            color: #ffffff;
          }
          .methodology-item p {
            color: #aab8c8;
          }
          @media (max-width: 768px) {
            h2 { font-size: 2.2rem !important; }
            .differentiator-image-box {
              height: 220px; /* Increased height for mobile */
              margin-bottom: 2rem;
            }
          }
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }
        `}
      </style>
      </div>
    </div>
  );
};

export default PhysicalVerificationPage;
