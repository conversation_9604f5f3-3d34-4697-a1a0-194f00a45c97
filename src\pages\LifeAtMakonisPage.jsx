import React, { useState, useEffect, useCallback } from "react";
import useScrollToTop from "../hooks/useScrollToTop";

const imageData = [
  {
    src: "src/Asserts/Life@mako/1.jpg",
    alt: "Makonis team working in the office",
  },
  {
    src: "src/Asserts/Life@mako/3.jpg",
    alt: "Makonis office space",
  },
  {
    src: "src/Asserts/Life@mako/4.jpg",
    alt: "Team collaboration at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/5.jpg",
    alt: "A celebratory moment at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/6.jpg",
    alt: "Makonis team event",
  },
  { src: "src/Asserts/Life@mako/7.jpg", alt: "A project discussion" },
  { src: "src/Asserts/Life@mako/8.jpg", alt: "Office amenities" },
  {
    src: "src/Asserts/Life@mako/9.jpg",
    alt: "Team building activity",
  },
  {
    src: "src/Asserts/Life@mako/11.jpg",
    alt: "Casual Friday at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/12.jpg",
    alt: "Team bonding activity",
  },
  {
    src: "src/Asserts/Life@mako/13.jpg",
    alt: "Team bonding activity",
  },
  {
    src: "src/Asserts/Life@mako/14.jpg",
    alt: "Team bonding activity",
  },
  {
    src: "src/Asserts/Life@mako/15.jpg",
    alt: "Makonis team working in the office",
  },
  {
    src: "src/Asserts/Life@mako/16.jpg",
    alt: "Makonis office space",
  },
  {
    src: "src/Asserts/Life@mako/18.jpg",
    alt: "Team collaboration at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/19.jpg",
    alt: "A celebratory moment at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/20.jpg",
    alt: "Makonis team event",
  },
  { src: "src/Asserts/Life@mako/21.jpg", alt: "A project discussion" },
  { src: "src/Asserts/Life@mako/22.jpg", alt: "Office amenities" },
  {
    src: "src/Asserts/Life@mako/23.jpg",
    alt: "Team building activity",
  },
  {
    src: "src/Asserts/Life@mako/24.jpg",
    alt: "Casual Friday at Makonis",
  },
  {
    src: "src/Asserts/Life@mako/25.jpg",
    alt: "Team bonding activity",
  },
  {
    src: "src/Asserts/Life@mako/26.jpg",
    alt: "Team bonding activity",
  },
  {
    src: "src/Asserts/Life@mako/28.jpg",
    alt: "Team bonding activity",
  },
  //  {
  //   src: "src/Asserts/Life@mako/27.mp4",
  //   alt: "Casual Friday at Makonis",
  // },
  // {
  //   src: "src/Asserts/Life@mako/17.mp4",
  //   alt: "Team bonding activity",
  // },
  // {
  //   src: "src/Asserts/Life@mako/10.mp4",
  //   alt: "Team bonding activity",
  // },
  // {
  //   src: "src/Asserts/Life@mako/2.mp4",
  //   alt: "Team bonding activity",
  // },
];

const LifeAtMakonisPage = () => {
  useScrollToTop();
  const [selectedImage, setSelectedImage] = useState(null);

  const handleNext = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex(
        (img) => img.src === selectedImage
      );
      const nextIndex = (currentIndex + 1) % imageData.length;
      setSelectedImage(imageData[nextIndex].src);
    }
  }, [selectedImage]);

  const handlePrev = useCallback(() => {
    if (selectedImage !== null) {
      const currentIndex = imageData.findIndex(
        (img) => img.src === selectedImage
      );
      const prevIndex =
        (currentIndex - 1 + imageData.length) % imageData.length;
      setSelectedImage(imageData[prevIndex].src);
    }
  }, [selectedImage]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!selectedImage) return;
      if (event.key === "ArrowRight") handleNext();
      if (event.key === "ArrowLeft") handlePrev();
      if (event.key === "Escape") setSelectedImage(null);
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [selectedImage, handleNext, handlePrev]);

  const accentRgb = "0, 160, 233";
  const backgroundColor = "#001a35";

  return (
    <div
      style={{
        backgroundColor: backgroundColor,
        backgroundImage: `radial-gradient(circle at 30% 100%, rgba(${accentRgb}, 0.15) 0%, transparent 40%), radial-gradient(circle at 90% 20%, rgba(${accentRgb}, 0.1) 0%, transparent 30%)`,
      }}
    >
      {/* Header Section */}
      <div className="relative h-screen w-full flex items-center justify-center">
        <div
          className="absolute inset-0 bg-cover bg-center filter blur-sm"
          style={{
            backgroundImage:
              "url('https://images.unsplash.com/photo-1519389950473-47ba0277781c?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8c29mdHdhcmUlMjBkZXZlbG9wbWVudCUyMGNvbXBhbnl8ZW58MHx8MHx8fDA%3D')",
          }}
        ></div>
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="relative z-10 text-center">
          <h1 className="text-5xl font-extrabold text-white md:text-7xl">
            Life @ Makonis
          </h1>
        </div>
      </div>

      {/* Grid Section */}
      <div className="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {imageData.map((image, index) => (
            <div
              key={`${image.src}-${index}`}
              className="overflow-hidden rounded-xl shadow-lg transition-transform duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer"
              onClick={() => setSelectedImage(image.src)}
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-60 object-cover"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Full-screen Image Modal */}
      {selectedImage && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "black",
            zIndex: 9999,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "0 2rem",
            boxSizing: "border-box",
          }}
          onClick={() => setSelectedImage(null)}
        >
          <img
            src={selectedImage}
            alt="Full screen view"
            style={{
              maxWidth: "100%",
              maxHeight: "100%",
              objectFit: "contain",
            }}
            onClick={(e) => e.stopPropagation()}
          />
          <div
            onClick={(e) => {
              e.stopPropagation();
              setSelectedImage(null);
            }}
            style={{
              position: "absolute",
              top: "20px",
              right: "20px",
              width: "40px",
              height: "40px",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              color: "white",
              borderRadius: "50%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              fontSize: "24px",
              cursor: "pointer",
              zIndex: 10000,
            }}
          >
            X
          </div>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handlePrev();
            }}
            style={{
              position: "absolute",
              left: "20px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
              zIndex: 10000,
              background: "rgba(0,0,0,0.5)",
              borderRadius: "50%",
              padding: "10px",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              style={{ height: "30px", width: "30px" }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="white"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </div>
          <div
            onClick={(e) => {
              e.stopPropagation();
              handleNext();
            }}
            style={{
              position: "absolute",
              right: "20px",
              top: "50%",
              transform: "translateY(-50%)",
              cursor: "pointer",
              zIndex: 10000,
              background: "rgba(0,0,0,0.5)",
              borderRadius: "50%",
              padding: "10px",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              style={{ height: "30px", width: "30px" }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="white"
              strokeWidth={2}
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      )}
    </div>
  );
};

export default LifeAtMakonisPage;
